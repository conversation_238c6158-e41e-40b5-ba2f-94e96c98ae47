rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Allow anyone to read files. This is useful for public assets like profile pictures.
    match /{allPaths=**} {
      allow read;
    }

    // Secure the user_uploads path.
    // Only authenticated users can write files here.
    match /user_uploads/{userId}/{fileName} {
      allow write: if request.auth != null && request.auth.uid == userId;
    }
  }
} 